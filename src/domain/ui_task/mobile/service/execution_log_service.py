#!/usr/bin/env python3
"""
执行日志服务 - 提供统一的JSON格式日志管理
"""
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any


class LogRole(Enum):
    """日志角色枚举"""
    DECISION_AGENT = "决策Agent"
    EXECUTION_AGENT = "执行Agent"
    SUPERVISOR_AGENT = "监督Agent"
    SYSTEM = "System"


class LogLevel(Enum):
    """日志级别枚举"""
    INFO = "info"
    ERROR = "error"
    WARNING = "warning"
    DEBUG = "debug"


class ExecutionLogService:
    """执行日志服务类"""

    @staticmethod
    def create_log_entry(role: LogRole, message: str, log_level: LogLevel = LogLevel.INFO) -> Dict[str, Any]:
        """
        创建单个日志条目

        Args:
            role: 日志角色
            message: 日志消息
            log_level: 日志级别

        Returns:
            日志条目字典
        """
        return {
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "log_level": log_level.value,
            "role": role.value,
            "message": message
        }

    @staticmethod
    def create_system_log(message: str, log_level: LogLevel = LogLevel.INFO) -> Dict[str, Any]:
        """
        创建系统日志

        Args:
            message: 系统消息
            log_level: 日志级别

        Returns:
            系统日志条目
        """
        return ExecutionLogService.create_log_entry(LogRole.SYSTEM, message, log_level)


    @staticmethod
    def create_task_completion_log(success: bool) -> Dict[str, Any]:
        """
        创建任务完成日志

        Args:
            success: 是否成功

        Returns:
            任务完成日志条目
        """
        status = "成功" if success else "失败"
        message = f"任务执行完成 - 状态: {status}"
        return ExecutionLogService.create_system_log(message)

    @staticmethod
    def create_task_stop_log() -> Dict[str, Any]:
        """
        创建任务停止日志

        Returns:
            任务停止日志条目
        """
        message = "任务停止 - 用户停止任务"
        return ExecutionLogService.create_system_log(message)

    @staticmethod
    def create_decision_log(step_name: str, action_decision: str, action: str, step_index: str = None) -> Dict[str, Any]:
        """
        创建决策Agent日志

        Args:
            step_name: 步骤名称
            action_decision: 执行决策
            action: 执行动作
            step_index: 步骤索引

        Returns:
            决策Agent日志条目
        """
        if step_index:
            formatted_step = f"{step_index}.{step_name}"
        else:
            formatted_step = step_name
        message = f"执行步骤: {formatted_step}\n执行决策: {action_decision}\n执行动作: {action}"
        return ExecutionLogService.create_log_entry(LogRole.DECISION_AGENT, message)

    @staticmethod
    def create_execution_log(thought_content: str) -> Dict[str, Any]:
        """
        创建执行Agent日志

        Args:
            thought_content: 执行思考内容

        Returns:
            执行Agent日志条目
        """
        message = f"执行思考: {thought_content}"
        return ExecutionLogService.create_log_entry(LogRole.EXECUTION_AGENT, message)

    @staticmethod
    def create_supervisor_log(result: str, reason: str) -> Dict[str, Any]:
        """
        创建监督Agent日志

        Args:
            result: 检测结果（通过/未通过）
            reason: 检测意见

        Returns:
            监督Agent日志条目
        """
        message = f"检测结果: {result}\n检测意见: {reason}"
        return ExecutionLogService.create_log_entry(LogRole.SUPERVISOR_AGENT, message)

    @staticmethod
    def create_error_log(error_message: str) -> Dict[str, Any]:
        """
        创建错误日志

        Args:
            error_message: 错误消息

        Returns:
            错误日志条目
        """
        return ExecutionLogService.create_system_log(error_message, LogLevel.ERROR)

    @staticmethod
    def parse_existing_text_log(text_log: str) -> List[Dict[str, Any]]:
        """
        解析现有的文本格式日志为JSON格式（用于数据迁移）

        Args:
            text_log: 文本格式的日志

        Returns:
            JSON格式的日志列表
        """
        if not text_log:
            return []

        logs = []
        lines = text_log.split('\n')
        current_timestamp = None
        current_role = None
        current_message_parts = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是时间戳行
            if line.startswith('[') and ']' in line:
                # 保存之前的角色条目
                if current_role and current_message_parts and current_timestamp:
                    entry = {
                        "datetime": current_timestamp,
                        "log_level": "info",
                        "role": current_role,
                        "message": "\n".join(current_message_parts)
                    }
                    logs.append(entry)

                # 解析新的时间戳
                timestamp_end = line.find(']')
                timestamp = line[1:timestamp_end]
                try:
                    # 尝试解析时间戳并转换为标准格式
                    if len(timestamp.split()) == 2:  # "12-25 14:30:15" 格式
                        current_year = datetime.now().year
                        timestamp = f"{current_year}-{timestamp}"
                    parsed_time = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
                    current_timestamp = parsed_time.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 重置当前状态
                current_role = None
                current_message_parts = []

                # 检查时间戳行后面是否有系统消息
                remaining_content = line[timestamp_end + 1:].strip()
                if remaining_content:
                    # 这是一个系统消息
                    system_entry = {
                        "datetime": current_timestamp,
                        "log_level": "info",
                        "role": "System",
                        "message": remaining_content
                    }
                    logs.append(system_entry)
                continue

            # 检查角色标识
            if line in ["决策Agent", "执行Agent", "监督Agent"]:
                # 保存之前的角色内容
                if current_role and current_message_parts and current_timestamp:
                    entry = {
                        "datetime": current_timestamp,
                        "log_level": "info",
                        "role": current_role,
                        "message": "\n".join(current_message_parts)
                    }
                    logs.append(entry)

                # 设置新角色
                current_role = line
                current_message_parts = []
                continue

            # 添加消息内容
            if current_role and current_timestamp:
                current_message_parts.append(line)

        # 保存最后一个条目
        if current_role and current_message_parts and current_timestamp:
            entry = {
                "datetime": current_timestamp,
                "log_level": "info",
                "role": current_role,
                "message": "\n".join(current_message_parts)
            }
            logs.append(entry)

        return logs