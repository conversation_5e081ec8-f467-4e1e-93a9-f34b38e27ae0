#!/usr/bin/env python3
"""
步骤优化聚合器
负责处理测试用例步骤优化的业务逻辑
"""

from typing import Tuple, Optional

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from src.infra.model import get_chat_model


class StepOptimizeAggregate:
    """步骤优化聚合器 - 处理步骤优化的核心业务逻辑"""

    def __init__(self):

        self.model = get_chat_model()

    def optimize_test_steps(self, steps: str) -> Tuple[Optional[str], Optional[str]]:
        """
        使用AI优化测试用例步骤格式

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepOptimizeAggregate: Starting step optimization for: {steps[:100]}...")

            # 构建优化提示
            system_instruction = self._build_optimization_prompt(steps)

            messages = [
                {
                    "role": "system",
                    "content": system_instruction
                }
            ]
            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model

            output = chain.invoke({"timeout": 30})
            ai_result = output.content

            logger.info(f"✅ StepOptimizeAggregate: Step optimization completed successfully: {ai_result}")
            return ai_result, None
        except Exception as e:
            logger.error(f"❌ StepOptimizeAggregate: Failed to optimize steps with AI: {str(e)}")
            return None, str(e)

    @staticmethod
    def _build_optimization_prompt(steps: str) -> str:
        """
        构建步骤优化的提示词

        Returns:
            优化提示词
        """
        return f"""
########## 角色 ##########
你是一个测试用例步骤优化助手，请你将用例步骤格式化，用换行将步骤进行分隔，一行一个步骤。

########## 优化要求 ##########
1.如果用例步骤包含换行，按照换行拆分，去除序号等前缀描述
2.如果用例步骤包含序号，但没有换行，则按照序号拆分，去除序号等前缀描述
3.仅修改换行和序号，不要修改步骤内容

########## 输出要求 ##########
仅输出优化后的步骤内容，每行一个步骤，不要在步骤末尾添加多余空格或空行。
输出为纯文本格式，除了步骤外不输出任何说明、标点或多余空白行。

########## 优化样例 ##########
1.用例步骤： 1.点击首页 \n 2、点击下方'热门玩法' 3 点击右上角'我的房间'
  优化结果：
   点击首页
   点击下方'热门玩法' 
   点击右上角'我的房间'
2.用例步骤： 1.点击首页 \n 点击下方'热门玩法' \n 点击右上角'我的房间'
  优化结果：
   点击首页
   点击下方'热门玩法' 
   点击右上角'我的房间'
   
   
########## 本次需要优化的用例步骤 ##########
{steps}
"""


# 创建全局实例
step_optimize_aggregate = StepOptimizeAggregate()
